#!/usr/bin/env node

/**
 * Test Isolation Verification Script
 *
 * This script verifies that test cleanup is working correctly by:
 * 1. Running the test suite multiple times consecutively
 * 2. Checking database state before and after each run
 * 3. Verifying no persistent data remains between runs
 * 4. Testing concurrent test execution for race conditions
 */

import { execSync } from 'child_process';

// Configuration
const TEST_RUNS = 3;
const CONCURRENT_RUNS = 2;

console.log('🔍 Starting Test Isolation Verification...\n');

interface CommandResult {
  success: boolean;
  output: string;
  error?: string;
}

interface ExecuteOptions {
  encoding?: BufferEncoding;
  stdio?: 'pipe' | 'inherit';
}

interface TestRunResult {
  run: number;
  preTestClean: boolean;
  testsPassed: boolean;
  postTestClean: boolean;
  output: boolean;
}

interface ConcurrentTestResult {
  run: number;
  success: boolean;
  hasCleanState: boolean;
}

/**
 * Execute a command and return the output
 */
function executeCommand(
  command: string,
  options: ExecuteOptions = {},
): CommandResult {
  try {
    const output = execSync(command, {
      encoding: 'utf8',
      stdio: 'pipe',
      ...options,
    });
    return { success: true, output };
  } catch (error: unknown) {
    const execError = error as {
      stdout?: string;
      stderr?: string;
      message: string;
    };
    return {
      success: false,
      output: execError.stdout ?? '',
      error: execError.stderr ?? execError.message,
    };
  }
}

/**
 * Run database verification query
 */
function verifyDatabaseState(): boolean {
  console.log('  📊 Checking database state...');

  // This would ideally connect to the database and check counts
  // For now, we'll rely on the test output showing "Initial quiz count: 0"
  const result = executeCommand(
    'npm run test:e2e -- --testPathPattern=database-cleanup.e2e-spec.ts --testNamePattern="should verify database is empty"',
  );

  if (result.success) {
    console.log('  ✅ Database verification passed');
    return true;
  } else {
    console.log('  ❌ Database verification failed');
    console.log('  Error:', result.error);
    return false;
  }
}

/**
 * Run consecutive test suite executions
 */
function runConsecutiveTests(): TestRunResult[] {
  console.log(`🔄 Running ${TEST_RUNS} consecutive test suite executions...\n`);

  const results: TestRunResult[] = [];

  for (let i = 1; i <= TEST_RUNS; i++) {
    console.log(`📋 Test Run ${i}/${TEST_RUNS}`);
    console.log('='.repeat(50));

    // Verify clean state before test
    const preTestClean = verifyDatabaseState();

    // Run the test suite with specific test that shows clean state indicators
    console.log('  🧪 Running test suite...');
    const testResult = executeCommand('npm run test:all');

    // Also run the specific test that shows debugging output
    console.log('  🔍 Checking clean state indicators...');
    const cleanStateResult = executeCommand(
      'npm run test:e2e -- --testPathPattern=quiz-upload.e2e-spec.ts --testNamePattern="should verify proper cleanup on database failures"',
    );

    // Verify clean state after test
    const postTestClean = verifyDatabaseState();

    const runResult: TestRunResult = {
      run: i,
      preTestClean,
      testsPassed: testResult.success,
      postTestClean,
      output:
        (cleanStateResult.output.includes('Initial quiz count: 0') &&
          cleanStateResult.output.includes('Initial asset count: 0')) ||
        (Boolean(cleanStateResult.error) &&
          cleanStateResult.error!.includes('Initial quiz count: 0') &&
          cleanStateResult.error!.includes('Initial asset count: 0')),
    };

    results.push(runResult);

    console.log(`  📊 Run ${i} Results:`);
    console.log(`    Pre-test clean: ${preTestClean ? '✅' : '❌'}`);
    console.log(`    Tests passed: ${runResult.testsPassed ? '✅' : '❌'}`);
    console.log(`    Post-test clean: ${postTestClean ? '✅' : '❌'}`);
    console.log(
      `    Clean state indicators: ${runResult.output ? '✅' : '❌'}`,
    );
    console.log('');
  }

  return results;
}

/**
 * Test concurrent execution for race conditions
 */
async function testConcurrentExecution(): Promise<ConcurrentTestResult[]> {
  console.log(
    `⚡ Testing concurrent test execution (${CONCURRENT_RUNS} parallel runs)...\n`,
  );

  const promises: Promise<ConcurrentTestResult>[] = [];
  for (let i = 1; i <= CONCURRENT_RUNS; i++) {
    promises.push(
      new Promise((resolve) => {
        const result = executeCommand(
          'npm run test:e2e -- --testPathPattern=quiz-upload.e2e-spec.ts --testNamePattern="should verify proper cleanup on database failures"',
        );
        resolve({
          run: i,
          success: result.success,
          hasCleanState:
            (result.output.includes('Initial quiz count: 0') &&
              result.output.includes('Initial asset count: 0')) ||
            (Boolean(result.error) &&
              result.error!.includes('Initial quiz count: 0') &&
              result.error!.includes('Initial asset count: 0')),
        });
      }),
    );
  }

  const results = await Promise.all(promises);

  console.log('📊 Concurrent Execution Results:');
  results.forEach((result) => {
    console.log(
      `  Run ${result.run}: ${result.success ? '✅' : '❌'} (Clean state: ${result.hasCleanState ? '✅' : '❌'})`,
    );
  });

  return results;
}

/**
 * Generate verification report
 */
function generateReport(
  consecutiveResults: TestRunResult[],
  concurrentResults: ConcurrentTestResult[],
): boolean {
  console.log('\n📋 TEST ISOLATION VERIFICATION REPORT');
  console.log('='.repeat(60));

  // Consecutive tests analysis
  const allPreTestClean = consecutiveResults.every((r) => r.preTestClean);
  const allPostTestClean = consecutiveResults.every((r) => r.postTestClean);
  const allTestsPassed = consecutiveResults.every((r) => r.testsPassed);
  const allHaveCleanIndicators = consecutiveResults.every((r) => r.output);

  console.log('\n🔄 Consecutive Test Runs:');
  console.log(`  Total runs: ${consecutiveResults.length}`);
  console.log(`  All pre-test clean: ${allPreTestClean ? '✅' : '❌'}`);
  console.log(`  All tests passed: ${allTestsPassed ? '✅' : '❌'}`);
  console.log(`  All post-test clean: ${allPostTestClean ? '✅' : '❌'}`);
  console.log(
    `  All show clean state indicators: ${allHaveCleanIndicators ? '✅' : '❌'}`,
  );

  // Concurrent tests analysis
  const allConcurrentPassed = concurrentResults.every((r) => r.success);
  const allConcurrentClean = concurrentResults.every((r) => r.hasCleanState);

  console.log('\n⚡ Concurrent Test Execution:');
  console.log(`  Total concurrent runs: ${concurrentResults.length}`);
  console.log(
    `  All concurrent tests passed: ${allConcurrentPassed ? '✅' : '❌'}`,
  );
  console.log(`  All show clean state: ${allConcurrentClean ? '✅' : '❌'}`);

  // Overall assessment
  const overallSuccess =
    allPreTestClean &&
    allPostTestClean &&
    allHaveCleanIndicators &&
    allConcurrentClean;

  console.log('\n🎯 OVERALL ASSESSMENT:');
  console.log(
    `  Test isolation: ${overallSuccess ? '✅ VERIFIED' : '❌ ISSUES DETECTED'}`,
  );
  console.log(
    `  Data cleanup: ${allPostTestClean && allHaveCleanIndicators ? '✅ WORKING' : '❌ NEEDS ATTENTION'}`,
  );
  console.log(
    `  Race conditions: ${allConcurrentClean ? '✅ NO ISSUES' : '❌ POTENTIAL ISSUES'}`,
  );

  if (overallSuccess) {
    console.log(
      '\n🎉 Test isolation verification PASSED! All tests are properly isolated.',
    );
  } else {
    console.log(
      '\n⚠️  Test isolation verification FAILED! Issues detected that need attention.',
    );
  }

  return overallSuccess;
}

/**
 * Main execution
 */
async function main(): Promise<void> {
  try {
    // Ensure database is running
    console.log('🔍 Ensuring database is ready...');
    const dbCheck = executeCommand('npx ts-node scripts/ensure-db-running.ts');
    if (!dbCheck.success) {
      console.error(
        '❌ Database is not ready. Please start the database first.',
      );
      process.exit(1);
    }
    console.log('✅ Database is ready\n');

    // Run consecutive tests
    const consecutiveResults = runConsecutiveTests();

    // Run concurrent tests
    const concurrentResults = await testConcurrentExecution();

    // Generate report
    const success = generateReport(consecutiveResults, concurrentResults);

    // Exit with appropriate code
    process.exit(success ? 0 : 1);
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    console.error('❌ Verification script failed:', errorMessage);
    process.exit(1);
  }
}

// Run the verification
void main();
