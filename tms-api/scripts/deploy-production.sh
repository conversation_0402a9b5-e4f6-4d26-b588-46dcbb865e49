#!/bin/bash

# Production Deployment Script for TMS REST API
# SECURITY: This script deploys the TMS API to production with security checks

set -e

echo "🚀 TMS REST API Production Deployment"
echo "====================================="

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if file exists
check_file() {
    if [ ! -f "$1" ]; then
        print_error "Required file not found: $1"
        exit 1
    fi
}

# Function to check if directory exists
check_directory() {
    if [ ! -d "$1" ]; then
        print_error "Required directory not found: $1"
        exit 1
    fi
}

echo ""
echo "🔍 Pre-deployment Security Checks"
echo "================================="

# Check if secrets are set up
echo "Checking production secrets..."
check_directory "secrets"
check_file "secrets/db_username.txt"
check_file "secrets/db_password.txt"
check_file "secrets/db_database.txt"
check_file "secrets/minio_access_key.txt"
check_file "secrets/minio_secret_key.txt"
check_file "secrets/minio_bucket.txt"
check_file "secrets/auth_username.txt"
check_file "secrets/auth_password.txt"
print_status "Production secrets configured"

# Check SSL certificates
echo "Checking SSL certificates..."
if [ ! -f "ssl/nginx/tms-api.crt" ] || [ ! -f "ssl/nginx/tms-api.key" ]; then
    print_warning "SSL certificates not found in ssl/nginx/"
    print_warning "You'll need to set up SSL certificates before production deployment"
    echo "For testing, you can generate self-signed certificates:"
    echo "  openssl req -x509 -nodes -days 365 -newkey rsa:2048 \\"
    echo "    -keyout ssl/nginx/tms-api.key \\"
    echo "    -out ssl/nginx/tms-api.crt \\"
    echo "    -subj '/CN=localhost'"
    echo ""
    read -p "Continue without SSL certificates? (y/N): " continue_without_ssl
    if [ "$continue_without_ssl" != "y" ] && [ "$continue_without_ssl" != "Y" ]; then
        print_error "Deployment cancelled. Please set up SSL certificates first."
        exit 1
    fi
else
    print_status "SSL certificates found"
fi

# Check Docker configuration files
echo "Checking Docker configuration..."
check_file "docker-compose.prod.yml"
check_file "Dockerfile"
check_file "nginx/nginx.conf"
print_status "Docker configuration files present"

# Check for hardcoded credentials
echo "Checking for hardcoded credentials..."
if grep -r "tms-username\|tms-password" src/ test/ 2>/dev/null | grep -v coverage; then
    print_error "Hardcoded credentials found in source code!"
    print_error "Please remove all hardcoded credentials before production deployment"
    exit 1
fi
print_status "No hardcoded credentials found in source code"

# Check environment configuration
echo "Checking production environment configuration..."
if [ ! -f ".env.production" ]; then
    print_warning ".env.production file not found"
    print_warning "Using Docker secrets instead (recommended for production)"
else
    print_status "Production environment file found"
fi

echo ""
echo "🏗️  Building Production Images"
echo "============================="

# Build production Docker image
echo "Building production Docker image..."
docker-compose -f docker-compose.prod.yml build --no-cache
print_status "Production image built successfully"

echo ""
echo "🧪 Running Pre-deployment Tests"
echo "==============================="

# Run tests to ensure everything works
echo "Running unit tests..."
npm test 2>/dev/null || {
    print_error "Unit tests failed! Please fix tests before deploying to production."
    exit 1
}
print_status "Unit tests passed"

echo ""
echo "🚀 Deploying to Production"
echo "=========================="

# Stop any existing production containers
echo "Stopping existing production containers..."
docker-compose -f docker-compose.prod.yml down 2>/dev/null || true

# Start production services
echo "Starting production services..."
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to start
echo "Waiting for services to start..."
sleep 30

# Health check
echo "Performing health check..."
if docker-compose -f docker-compose.prod.yml exec -T api curl -f http://localhost:3000/health >/dev/null 2>&1; then
    print_status "Health check passed"
else
    print_error "Health check failed!"
    echo "Checking container logs..."
    docker-compose -f docker-compose.prod.yml logs api
    exit 1
fi

echo ""
echo "✅ Production Deployment Completed Successfully!"
echo "=============================================="
echo ""
echo "📊 Deployment Summary:"
echo "• API: Running on https://localhost (via reverse proxy)"
echo "• Database: PostgreSQL with secure credentials"
echo "• Storage: MinIO with SSL enabled"
echo "• Security: HTTPS, rate limiting, security headers enabled"
echo ""
echo "🔧 Management Commands:"
echo "• View logs: docker-compose -f docker-compose.prod.yml logs -f"
echo "• Stop services: docker-compose -f docker-compose.prod.yml down"
echo "• Restart services: docker-compose -f docker-compose.prod.yml restart"
echo ""
echo "🔒 Security Reminders:"
echo "• Monitor logs regularly for security events"
echo "• Keep SSL certificates updated"
echo "• Rotate credentials regularly"
echo "• Review and update security configurations periodically"
echo ""
