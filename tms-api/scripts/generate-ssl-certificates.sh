#!/bin/bash

# SSL Certificate Generation Script for TMS REST API
# This script generates self-signed SSL certificates for development and testing
# For production, use proper SSL certificates from a trusted CA (e.g., Let's Encrypt)

set -e

echo "🔐 Generating SSL certificates for TMS REST API..."

# Create SSL directories if they don't exist
mkdir -p ssl/nginx
mkdir -p ssl/minio

# Function to generate certificates
generate_certificate() {
    local cert_path="$1"
    local key_path="$2"
    local common_name="$3"
    local description="$4"
    
    echo "Generating $description certificate..."
    
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout "$key_path" \
        -out "$cert_path" \
        -subj "/C=AU/ST=NSW/L=Sydney/O=TMS Development/OU=IT Department/CN=$common_name" \
        -addext "subjectAltName=DNS:localhost,DNS:api,DNS:minio,IP:127.0.0.1"
    
    # Set secure permissions
    chmod 600 "$key_path"
    chmod 644 "$cert_path"
    
    echo "✅ $description certificate generated successfully"
}

# Generate nginx SSL certificate
generate_certificate \
    "ssl/nginx/tms-api.crt" \
    "ssl/nginx/tms-api.key" \
    "localhost" \
    "nginx reverse proxy"

# Generate MinIO SSL certificate
generate_certificate \
    "ssl/minio/public.crt" \
    "ssl/minio/private.key" \
    "minio" \
    "MinIO object storage"

echo ""
echo "✅ SSL Certificate Generation Completed!"
echo "======================================="
echo ""
echo "📋 Generated Certificates:"
echo "• nginx: ssl/nginx/tms-api.crt, ssl/nginx/tms-api.key"
echo "• MinIO: ssl/minio/public.crt, ssl/minio/private.key"
echo ""
echo "⚠️  IMPORTANT SECURITY NOTES:"
echo "• These are self-signed certificates for development/testing only"
echo "• Browsers will show security warnings for self-signed certificates"
echo "• For production, obtain proper SSL certificates from a trusted CA"
echo "• Consider using Let's Encrypt for free, automated SSL certificates"
echo ""
echo "🔧 Next Steps:"
echo "1. Start production services: docker-compose -f docker-compose.prod.yml up -d"
echo "2. Access API via HTTPS: https://localhost/health"
echo "3. For production deployment, replace with proper SSL certificates"
echo ""
echo "🔒 Certificate Details:"
echo "• Validity: 365 days from generation date"
echo "• Key Size: 2048-bit RSA"
echo "• Subject Alternative Names: localhost, api, minio, 127.0.0.1"
echo "• Organization: TMS Development"
echo ""
