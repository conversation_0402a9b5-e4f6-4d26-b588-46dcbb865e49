#!/bin/bash

# Production Secrets Setup Script for TMS REST API
# SECURITY: This script sets up Docker secrets for production deployment
# Run this script before deploying to production

set -e

echo "🔐 Setting up production secrets for TMS REST API..."

# Create secrets directory if it doesn't exist
mkdir -p secrets

# Function to generate secure random password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

# Function to prompt for input with default
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    read -p "$prompt [$default]: " input
    if [ -z "$input" ]; then
        eval "$var_name='$default'"
    else
        eval "$var_name='$input'"
    fi
}

echo ""
echo "📋 Production Configuration Setup"
echo "=================================="

# Database configuration
echo ""
echo "🗄️  Database Configuration:"
prompt_with_default "Database username" "tms_prod_db_user" DB_USERNAME
prompt_with_default "Database password (leave empty to generate)" "" DB_PASSWORD
if [ -z "$DB_PASSWORD" ]; then
    DB_PASSWORD=$(generate_password)
    echo "Generated secure database password: $DB_PASSWORD"
fi
prompt_with_default "Database name" "tms_production_database" DB_DATABASE

# MinIO configuration
echo ""
echo "📦 MinIO Configuration:"
prompt_with_default "MinIO access key" "tms_prod_minio_access" MINIO_ACCESS_KEY
prompt_with_default "MinIO secret key (leave empty to generate)" "" MINIO_SECRET_KEY
if [ -z "$MINIO_SECRET_KEY" ]; then
    MINIO_SECRET_KEY=$(generate_password)
    echo "Generated secure MinIO secret key: $MINIO_SECRET_KEY"
fi
prompt_with_default "MinIO bucket name" "tms_production_bucket" MINIO_BUCKET

# API Authentication configuration
echo ""
echo "🔑 API Authentication Configuration:"
prompt_with_default "API username" "tms_prod_api_user" AUTH_USERNAME
prompt_with_default "API password (leave empty to generate)" "" AUTH_PASSWORD
if [ -z "$AUTH_PASSWORD" ]; then
    AUTH_PASSWORD=$(generate_password)
    echo "Generated secure API password: $AUTH_PASSWORD"
fi

echo ""
echo "💾 Writing secrets to files..."

# Write secrets to files
echo "$DB_USERNAME" > secrets/db_username.txt
echo "$DB_PASSWORD" > secrets/db_password.txt
echo "$DB_DATABASE" > secrets/db_database.txt
echo "$MINIO_ACCESS_KEY" > secrets/minio_access_key.txt
echo "$MINIO_SECRET_KEY" > secrets/minio_secret_key.txt
echo "$MINIO_BUCKET" > secrets/minio_bucket.txt
echo "$AUTH_USERNAME" > secrets/auth_username.txt
echo "$AUTH_PASSWORD" > secrets/auth_password.txt

# Set secure permissions
chmod 600 secrets/*.txt

echo ""
echo "✅ Production secrets setup completed!"
echo ""
echo "📋 Summary:"
echo "==========="
echo "Database User: $DB_USERNAME"
echo "Database Name: $DB_DATABASE"
echo "MinIO Access Key: $MINIO_ACCESS_KEY"
echo "MinIO Bucket: $MINIO_BUCKET"
echo "API Username: $AUTH_USERNAME"
echo ""
echo "🔒 Passwords have been securely generated and stored in secrets/ directory"
echo "⚠️  IMPORTANT: Keep these credentials secure and never commit them to version control"
echo ""
echo "🚀 Next steps:"
echo "1. Set up SSL certificates in ssl/nginx/ directory"
echo "2. Review and customize nginx/nginx.conf if needed"
echo "3. Run: docker-compose -f docker-compose.prod.yml up -d"
echo ""
